import os
# Disable torch compilation before importing torch
os.environ["TORCH_COMPILE_DISABLE"] = "1"

import torch
import traceback
from transformers import AutoProcessor, AutoModelForImageTextToText
from PIL import Image
import requests

# Disable dynamo compilation to avoid compilation issues
import torch._dynamo
torch._dynamo.config.suppress_errors = True
torch._dynamo.config.disable = True

# Monkey patch to fix the ALL_PARALLEL_STYLES issue
def patch_parallel_styles():
    try:
        from transformers.modeling_utils import ALL_PARALLEL_STYLES
        if ALL_PARALLEL_STYLES is None:
            # Set it to include common parallel styles
            import transformers.modeling_utils
            transformers.modeling_utils.ALL_PARALLEL_STYLES = [
                "colwise", "rowwise", "sequence", "replicate", "shard"
            ]
            print("Patched ALL_PARALLEL_STYLES from None to include common styles")
        elif isinstance(ALL_PARALLEL_STYLES, list) and len(ALL_PARALLEL_STYLES) == 0:
            # If it's an empty list, add common styles
            import transformers.modeling_utils
            transformers.modeling_utils.ALL_PARALLEL_STYLES = [
                "colwise", "rowwise", "sequence", "replicate", "shard"
            ]
            print("Patched ALL_PARALLEL_STYLES from empty list to include common styles")
    except ImportError:
        print("Could not import ALL_PARALLEL_STYLES, skipping patch")
    except Exception as e:
        print(f"Error patching ALL_PARALLEL_STYLES: {e}")

# Apply the patch
patch_parallel_styles()

model_id = "google/medgemma-4b-it"

print(f"Loading model: {model_id}")
print("This may take a few minutes...")

try:
    # Load the model with error handling
    model = AutoModelForImageTextToText.from_pretrained(
        model_id,
        torch_dtype=torch.bfloat16,
        device_map="auto",
        token=True  # Use HuggingFace CLI login
    )
    print("Model loaded successfully!")
    
    # Load the processor
    processor = AutoProcessor.from_pretrained(model_id, token=True)
    print("Processor loaded successfully!")
    
except Exception as e:
    print(f"Error loading model or processor: {e}")
    print("Make sure you have:")
    print("1. Logged in with: huggingface-cli login")
    print("2. Accepted the model license on HuggingFace Hub")
    print("3. Installed required packages: pip install accelerate")
    traceback.print_exc()
    exit()

try:
    # Image attribution: Stillwaterising, CC0, via Wikimedia Commons
    image_url = "https://upload.wikimedia.org/wikipedia/commons/c/c8/Chest_Xray_PA_3-8-2010.png"
    print(f"Downloading image from: {image_url}")
    
    response = requests.get(image_url, headers={"User-Agent": "Mozilla/5.0"}, stream=True)
    response.raise_for_status()
    image = Image.open(response.raw)
    print("Image loaded successfully!")
    
    # Prepare the messages
    messages = [
        {
            "role": "system",
            "content": [{"type": "text", "text": "You are an expert radiologist."}]
        },
        {
            "role": "user",
            "content": [
                {"type": "text", "text": "Describe this X-ray"},
                {"type": "image", "image": image}
            ]
        }
    ]
    
    print("Processing inputs...")
    inputs = processor.apply_chat_template(
        messages, 
        add_generation_prompt=True, 
        tokenize=True,
        return_dict=True, 
        return_tensors="pt"
    ).to(model.device, dtype=torch.bfloat16)
    
    input_len = inputs["input_ids"].shape[-1]
    print(f"Input length: {input_len} tokens")
    
    print("Generating response...")
    with torch.inference_mode():
        generation = model.generate(
            **inputs, 
            max_new_tokens=200, 
            do_sample=False,
            pad_token_id=processor.tokenizer.eos_token_id
        )
        generation = generation[0][input_len:]
    
    decoded = processor.decode(generation, skip_special_tokens=True)
    print("\n" + "="*50)
    print("RADIOLOGIST ANALYSIS:")
    print("="*50)
    print(decoded)
    print("="*50)
    
except Exception as e:
    print(f"Error during image processing or generation: {e}")
    traceback.print_exc()
