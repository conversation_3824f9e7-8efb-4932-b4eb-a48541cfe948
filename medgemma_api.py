import os
# Disable torch compilation before importing torch
os.environ["TORCH_COMPILE_DISABLE"] = "1"

import torch
import traceback
from fastapi import Fast<PERSON><PERSON>, HTTPException, File, UploadFile, Form
from fastapi.responses import J<PERSON>NResponse
from pydantic import BaseModel
from typing import Optional
import uvicorn
from PIL import Image
import io
import base64
from transformers import AutoProcessor, AutoModelForImageTextToText, AutoTokenizer, AutoModelForCausalLM

# Disable dynamo compilation to avoid compilation issues
import torch._dynamo
torch._dynamo.config.suppress_errors = True
torch._dynamo.config.disable = True

# Monkey patch to fix the ALL_PARALLEL_STYLES issue
def patch_parallel_styles():
    try:
        from transformers.modeling_utils import ALL_PARALLEL_STYLES
        if ALL_PARALLEL_STYLES is None:
            import transformers.modeling_utils
            transformers.modeling_utils.ALL_PARALLEL_STYLES = [
                "colwise", "rowwise", "sequence", "replicate", "shard"
            ]
            print("Patched ALL_PARALLEL_STYLES from None to include common styles")
        elif isinstance(ALL_PARALLEL_STYLES, list) and len(ALL_PARALLEL_STYLES) == 0:
            import transformers.modeling_utils
            transformers.modeling_utils.ALL_PARALLEL_STYLES = [
                "colwise", "rowwise", "sequence", "replicate", "shard"
            ]
            print("Patched ALL_PARALLEL_STYLES from empty list to include common styles")
    except ImportError:
        print("Could not import ALL_PARALLEL_STYLES, skipping patch")
    except Exception as e:
        print(f"Error patching ALL_PARALLEL_STYLES: {e}")

# Apply the patch
patch_parallel_styles()

# Initialize FastAPI app
app = FastAPI(
    title="MedGemma Medical AI API",
    description="AI-powered medical consultation API using Google's MedGemma model",
    version="1.0.0"
)

# Global variables for models
vision_model = None
vision_processor = None
text_model = None
text_tokenizer = None

# Pydantic models for request/response
class TextQuery(BaseModel):
    question: str
    max_tokens: Optional[int] = 200
    temperature: Optional[float] = 0.7

class TextResponse(BaseModel):
    answer: str
    model_used: str

class ImageAnalysisResponse(BaseModel):
    analysis: str
    model_used: str

@app.on_event("startup")
async def load_models():
    """Load the MedGemma models on startup"""
    global vision_model, vision_processor, text_model, text_tokenizer
    
    model_id = "google/medgemma-4b-it"
    print(f"Loading MedGemma models: {model_id}")
    
    try:
        # Load vision model for image-text tasks
        print("Loading vision model...")
        vision_model = AutoModelForImageTextToText.from_pretrained(
            model_id,
            torch_dtype=torch.bfloat16,
            device_map="auto",
            token=True
        )
        vision_processor = AutoProcessor.from_pretrained(model_id, token=True)
        print("Vision model loaded successfully!")
        
        # Load text-only model for faster text responses
        print("Loading text model...")
        text_model = AutoModelForCausalLM.from_pretrained(
            model_id,
            torch_dtype=torch.bfloat16,
            device_map="auto",
            token=True
        )
        text_tokenizer = AutoTokenizer.from_pretrained(model_id, token=True)
        print("Text model loaded successfully!")
        
    except Exception as e:
        print(f"Error loading models: {e}")
        traceback.print_exc()
        raise e

@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "message": "MedGemma Medical AI API is running",
        "status": "healthy",
        "models_loaded": {
            "vision_model": vision_model is not None,
            "text_model": text_model is not None
        }
    }

@app.post("/ask", response_model=TextResponse)
async def ask_medical_question(query: TextQuery):
    """Ask a text-based medical question"""
    if text_model is None or text_tokenizer is None:
        raise HTTPException(status_code=503, detail="Text model not loaded")
    
    try:
        # Format the prompt for medical consultation
        chat = [
            {"role": "user", "content": query.question}
        ]
        
        prompt = text_tokenizer.apply_chat_template(
            chat, 
            tokenize=False, 
            add_generation_prompt=True
        )
        
        # Tokenize and generate
        inputs = text_tokenizer(prompt, return_tensors="pt").to(text_model.device)
        
        with torch.inference_mode():
            outputs = text_model.generate(
                **inputs,
                max_new_tokens=query.max_tokens,
                do_sample=True,
                temperature=query.temperature,
                top_k=50,
                top_p=0.95,
                eos_token_id=text_tokenizer.eos_token_id,
                pad_token_id=text_tokenizer.eos_token_id
            )
        
        # Decode only the new tokens
        response_text = text_tokenizer.decode(
            outputs[0][inputs.input_ids.shape[1]:], 
            skip_special_tokens=True
        )
        
        return TextResponse(
            answer=response_text.strip(),
            model_used="medgemma-4b-it-text"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating response: {str(e)}")

@app.post("/analyze-image", response_model=ImageAnalysisResponse)
async def analyze_medical_image(
    file: UploadFile = File(...),
    question: str = Form("Describe this medical image and provide a professional analysis")
):
    """Analyze a medical image with optional custom question"""
    if vision_model is None or vision_processor is None:
        raise HTTPException(status_code=503, detail="Vision model not loaded")
    
    # Validate file type
    if not file.content_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="File must be an image")
    
    try:
        # Read and process the image
        image_data = await file.read()
        image = Image.open(io.BytesIO(image_data))
        
        # Convert to RGB if necessary
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # Prepare messages for the model
        messages = [
            {
                "role": "system",
                "content": [{"type": "text", "text": "You are an expert medical professional analyzing medical images."}]
            },
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": question},
                    {"type": "image", "image": image}
                ]
            }
        ]
        
        # Process inputs
        inputs = vision_processor.apply_chat_template(
            messages,
            add_generation_prompt=True,
            tokenize=True,
            return_dict=True,
            return_tensors="pt"
        ).to(vision_model.device, dtype=torch.bfloat16)
        
        input_len = inputs["input_ids"].shape[-1]
        
        # Generate response
        with torch.inference_mode():
            generation = vision_model.generate(
                **inputs,
                max_new_tokens=300,
                do_sample=False,
                pad_token_id=vision_processor.tokenizer.eos_token_id
            )
            generation = generation[0][input_len:]
        
        # Decode response
        analysis = vision_processor.decode(generation, skip_special_tokens=True)
        
        return ImageAnalysisResponse(
            analysis=analysis.strip(),
            model_used="medgemma-4b-it-vision"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error analyzing image: {str(e)}")

@app.get("/health")
async def health_check():
    """Detailed health check"""
    return {
        "status": "healthy",
        "models": {
            "vision_model_loaded": vision_model is not None,
            "vision_processor_loaded": vision_processor is not None,
            "text_model_loaded": text_model is not None,
            "text_tokenizer_loaded": text_tokenizer is not None
        },
        "gpu_available": torch.cuda.is_available(),
        "gpu_count": torch.cuda.device_count() if torch.cuda.is_available() else 0
    }

if __name__ == "__main__":
    print("Starting MedGemma Medical AI API...")
    print("Make sure you have:")
    print("1. Logged in with: huggingface-cli login")
    print("2. Accepted the model license on HuggingFace Hub")
    print("3. Installed required packages: pip install fastapi uvicorn python-multipart")
    
    uvicorn.run(
        app, 
        host="0.0.0.0", 
        port=8000,
        log_level="info"
    )
