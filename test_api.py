import requests
import json
from PIL import Image
import io

# API base URL
BASE_URL = "http://localhost:8000"

def test_health_check():
    """Test the health check endpoint"""
    print("Testing health check...")
    response = requests.get(f"{BASE_URL}/health")
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    print("-" * 50)

def test_text_question():
    """Test asking a text-based medical question"""
    print("Testing text-based medical question...")
    
    question_data = {
        "question": "What are the common symptoms of pneumonia and how is it diagnosed?",
        "max_tokens": 200,
        "temperature": 0.7
    }
    
    response = requests.post(
        f"{BASE_URL}/ask",
        json=question_data,
        headers={"Content-Type": "application/json"}
    )
    
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"Answer: {result['answer']}")
        print(f"Model used: {result['model_used']}")
    else:
        print(f"Error: {response.text}")
    print("-" * 50)

def test_image_analysis():
    """Test analyzing a medical image"""
    print("Testing medical image analysis...")
    
    # Download a sample chest X-ray
    image_url = "https://upload.wikimedia.org/wikipedia/commons/c/c8/Chest_Xray_PA_3-8-2010.png"
    
    try:
        # Download the image
        img_response = requests.get(image_url, headers={"User-Agent": "Mozilla/5.0"})
        img_response.raise_for_status()
        
        # Prepare the files and data for the API request
        files = {
            'file': ('chest_xray.png', img_response.content, 'image/png')
        }
        data = {
            'question': 'Please provide a detailed radiological analysis of this chest X-ray'
        }
        
        # Send request to API
        response = requests.post(
            f"{BASE_URL}/analyze-image",
            files=files,
            data=data
        )
        
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Analysis: {result['analysis']}")
            print(f"Model used: {result['model_used']}")
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"Error downloading or processing image: {e}")
    
    print("-" * 50)

def main():
    """Run all tests"""
    print("MedGemma API Test Client")
    print("=" * 50)
    
    # Test health check first
    test_health_check()
    
    # Test text question
    test_text_question()
    
    # Test image analysis
    test_image_analysis()
    
    print("All tests completed!")

if __name__ == "__main__":
    main()
