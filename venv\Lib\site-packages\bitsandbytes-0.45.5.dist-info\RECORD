bitsandbytes-0.45.5.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
bitsandbytes-0.45.5.dist-info/METADATA,sha256=H_fJh_w-4rfdGU_5VE3Q_3BMk5v-Dv6WbcwsT4sD410,5082
bitsandbytes-0.45.5.dist-info/RECORD,,
bitsandbytes-0.45.5.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
bitsandbytes-0.45.5.dist-info/WHEEL,sha256=Uot1xtv2NjZkWGfsbtixFxGSupJDmWFZZuntJdtO4yI,98
bitsandbytes-0.45.5.dist-info/licenses/LICENSE,sha256=8IqUv7C9wdfxy1m62vD4xVGxDbhxGFYi2_H9A2r_qcs,1107
bitsandbytes-0.45.5.dist-info/licenses/NOTICE.md,sha256=OpIf_XSa2lQICqX1wlMfagqddG9fsg_O4voC0qsvgSo,268
bitsandbytes-0.45.5.dist-info/top_level.txt,sha256=bK-Zzu-JyIIh4njm8jTYcbuqX-Z80XTcDal4lXCG0-M,13
bitsandbytes/__init__.py,sha256=h8ZBWNM_XVJ2GmLj6j9kpE8molESK4qiRYyOmKfpCck,568
bitsandbytes/__main__.py,sha256=_ObgXmW-NG395jj_oP86gSOw2LgqMtrGBmpjZOiRcuY,94
bitsandbytes/__pycache__/__init__.cpython-312.pyc,,
bitsandbytes/__pycache__/__main__.cpython-312.pyc,,
bitsandbytes/__pycache__/cextension.cpython-312.pyc,,
bitsandbytes/__pycache__/consts.cpython-312.pyc,,
bitsandbytes/__pycache__/cuda_specs.cpython-312.pyc,,
bitsandbytes/__pycache__/functional.cpython-312.pyc,,
bitsandbytes/__pycache__/utils.cpython-312.pyc,,
bitsandbytes/autograd/__init__.py,sha256=OeUeF5-AE6e2Y99DYsWDb20dwvot38GY2xpJUHUrG3Q,68
bitsandbytes/autograd/__pycache__/__init__.cpython-312.pyc,,
bitsandbytes/autograd/__pycache__/_functions.cpython-312.pyc,,
bitsandbytes/autograd/_functions.py,sha256=H-nU0WcN2Nr-plJwkvGz9imymOh35jF2Nx90zFej8dA,20793
bitsandbytes/cextension.py,sha256=X1S0SdvxaMgz-S0AmajKflUDsHB2bLPrxlm8iTFKYzc,3767
bitsandbytes/consts.py,sha256=hhASlIvnZUQd4XXo1wlQ86KpSfVSUCW91F7-wVjfjlk,392
bitsandbytes/cuda_specs.py,sha256=8i3VHz7spxYpEnWj-mFNy5bSzAP7P0IL7WMhn2WPWLk,1233
bitsandbytes/diagnostics/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
bitsandbytes/diagnostics/__pycache__/__init__.cpython-312.pyc,,
bitsandbytes/diagnostics/__pycache__/cuda.cpython-312.pyc,,
bitsandbytes/diagnostics/__pycache__/main.cpython-312.pyc,,
bitsandbytes/diagnostics/__pycache__/utils.cpython-312.pyc,,
bitsandbytes/diagnostics/cuda.py,sha256=dA2LtzC89miodjstIE8Pi3MHtFte2G05s6sxWhWfG88,7013
bitsandbytes/diagnostics/main.py,sha256=fAoGxpH-ctOihq6yB1y7-DRuxw9fZ_GRAnFsTfSDln8,2727
bitsandbytes/diagnostics/utils.py,sha256=eTZVw1AS-esmmpNj_NwCWX0nl_LP3aL-e83NjBiiulM,296
bitsandbytes/functional.py,sha256=XfLFfR4fEwmqcDIyyky-kfrocykhZ_QKeExp7m0Jxfs,110898
bitsandbytes/libbitsandbytes_cpu.dll,sha256=6rVxsN11wepraDCQKXsyAbIhhpKNoYezJEUkAzhrCzU,37376
bitsandbytes/libbitsandbytes_cuda117.dll,sha256=7VnvCesZbz3bY2DXaNlKxH04bVRSsNrQmDh_NlZGRV4,20764672
bitsandbytes/libbitsandbytes_cuda118.dll,sha256=3DFHWC_DDH9OfLPrJUnlo0AHYqPauJKqqa16dwhnrRI,26361856
bitsandbytes/libbitsandbytes_cuda120.dll,sha256=8J7m3fIScyzy2j2ocMp_dYoSJmBXCsm0zq-NH7l1nBk,25628672
bitsandbytes/libbitsandbytes_cuda121.dll,sha256=I48osTRSa_6I2buvKCS5pdK8OPN2-xPga4qfs7bB8Bo,25638400
bitsandbytes/libbitsandbytes_cuda122.dll,sha256=0-5SUV2xM7K4OzmWbVqPpRVzAw3XBhabSYpv1zlBOFA,25645568
bitsandbytes/libbitsandbytes_cuda123.dll,sha256=pNj1f2l1DQsaggdzNsUtYtNpRHBt8WAvk9crth8y8mc,25653760
bitsandbytes/libbitsandbytes_cuda124.dll,sha256=Q2qqlJmTL9u0xEeinXH8tc_a3S6XL5Mtb62WTrezN08,24887296
bitsandbytes/libbitsandbytes_cuda125.dll,sha256=PnWHO8NeRpbhFVl0SkQAVNIshguikqsmQ42D3u74G7k,24948224
bitsandbytes/libbitsandbytes_cuda126.dll,sha256=-c5L2L-dt805sy-6L1N1FMidKTZwsWt78zaf-_gHJos,24973824
bitsandbytes/libbitsandbytes_cuda128.dll,sha256=uExta3K8z4bDatMnYWSkcBChc1wySeQ52Wtw6T5BpPI,23552512
bitsandbytes/nn/__init__.py,sha256=VpgW4WzMXZ2KO_1aNS5K7-0ZMoXQctU064hISBAzubU,623
bitsandbytes/nn/__pycache__/__init__.cpython-312.pyc,,
bitsandbytes/nn/__pycache__/modules.cpython-312.pyc,,
bitsandbytes/nn/__pycache__/triton_based_modules.cpython-312.pyc,,
bitsandbytes/nn/modules.py,sha256=VMgtB54OPaLHmnRlLFwNyCxmB3ocHq4d-LLCRpIpmv0,38255
bitsandbytes/nn/triton_based_modules.py,sha256=nHgsatAx6SDjF3T9WT0-gc1wyTpFkMxt5Ioxb-n6bno,10082
bitsandbytes/optim/__init__.py,sha256=AvB75Z18WaU0EfmE5tH73CwLQxyhLFznOc2VZ97Ovcw,903
bitsandbytes/optim/__pycache__/__init__.cpython-312.pyc,,
bitsandbytes/optim/__pycache__/adagrad.cpython-312.pyc,,
bitsandbytes/optim/__pycache__/adam.cpython-312.pyc,,
bitsandbytes/optim/__pycache__/adamw.cpython-312.pyc,,
bitsandbytes/optim/__pycache__/ademamix.cpython-312.pyc,,
bitsandbytes/optim/__pycache__/lamb.cpython-312.pyc,,
bitsandbytes/optim/__pycache__/lars.cpython-312.pyc,,
bitsandbytes/optim/__pycache__/lion.cpython-312.pyc,,
bitsandbytes/optim/__pycache__/optimizer.cpython-312.pyc,,
bitsandbytes/optim/__pycache__/rmsprop.cpython-312.pyc,,
bitsandbytes/optim/__pycache__/sgd.cpython-312.pyc,,
bitsandbytes/optim/adagrad.py,sha256=bHW6e66lQv9DBU2QVaTBY--3HAItkrvCZ-oi50bLyc0,8122
bitsandbytes/optim/adam.py,sha256=k6KvGjh9WI8qwUra_pJmcucIUTdxdyIzNzhiMK3BNmU,24469
bitsandbytes/optim/adamw.py,sha256=R9rNgRV1kaYiSw0rgFYRHYRTNE_Fa8kk10MfRyc8lBQ,14894
bitsandbytes/optim/ademamix.py,sha256=nHwVILNTefwbImwIbNyZi8d5jcTjLlnTWf_i-WksF18,13385
bitsandbytes/optim/lamb.py,sha256=PrxexS_YODc4r3ev5ii4AxTMPqmKAS6ESag0GvBXRds,8161
bitsandbytes/optim/lars.py,sha256=4rmMYBz_5CzlJZboT8XqLrt7s6qLu_54FWY7lyJn2gY,9667
bitsandbytes/optim/lion.py,sha256=dujq4h6nONJHpKAgWCYNJy8O8LUSeruJ7kLD8kFGF0Q,11931
bitsandbytes/optim/optimizer.py,sha256=ncCcPE-oR5B3ZPCl1_m2VaW2_VbJ2qCE-9E9FXnHrYc,31076
bitsandbytes/optim/rmsprop.py,sha256=ewxuwdPQMuL_gRSGEAAlKHrBMPpjR37BEyFFJbcDcbo,7982
bitsandbytes/optim/sgd.py,sha256=YIdIlQRbxWqP-HvV4dJ_pYXeTId1IrACefpc1z0IHgw,6637
bitsandbytes/research/__init__.py,sha256=jSLMzRcp8QOd1VbiQwcOFrQH0fl1pfBxt23c-GxxWmo,125
bitsandbytes/research/__pycache__/__init__.cpython-312.pyc,,
bitsandbytes/research/autograd/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
bitsandbytes/research/autograd/__pycache__/__init__.cpython-312.pyc,,
bitsandbytes/research/autograd/__pycache__/_functions.cpython-312.pyc,,
bitsandbytes/research/autograd/_functions.py,sha256=agApMsEopZP-IvlYkJ7_IChN97FVXHc2kq8Wr-ZPgBI,14744
bitsandbytes/research/nn/__init__.py,sha256=_93TgkJ0CiQHJ3DL2VWoIqLE8urYCmXMNymVQiJSh4E,54
bitsandbytes/research/nn/__pycache__/__init__.cpython-312.pyc,,
bitsandbytes/research/nn/__pycache__/modules.cpython-312.pyc,,
bitsandbytes/research/nn/modules.py,sha256=13EgrXiYCuDbRewxTLvD7Bl-ltnkMN_8Tbm2Qx3MleA,2404
bitsandbytes/triton/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
bitsandbytes/triton/__pycache__/__init__.cpython-312.pyc,,
bitsandbytes/triton/__pycache__/dequantize_rowwise.cpython-312.pyc,,
bitsandbytes/triton/__pycache__/int8_matmul_mixed_dequantize.cpython-312.pyc,,
bitsandbytes/triton/__pycache__/int8_matmul_rowwise_dequantize.cpython-312.pyc,,
bitsandbytes/triton/__pycache__/matmul_perf_model.cpython-312.pyc,,
bitsandbytes/triton/__pycache__/quantize_columnwise_and_transpose.cpython-312.pyc,,
bitsandbytes/triton/__pycache__/quantize_global.cpython-312.pyc,,
bitsandbytes/triton/__pycache__/quantize_rowwise.cpython-312.pyc,,
bitsandbytes/triton/__pycache__/triton_utils.cpython-312.pyc,,
bitsandbytes/triton/dequantize_rowwise.py,sha256=YDVUsbRiRrmvH9yM-KqTbgfLt0VUounuOUiU2XlQB84,2104
bitsandbytes/triton/int8_matmul_mixed_dequantize.py,sha256=pfZna4Lg0nnbpFD2jPMHolobTcfCskcFf0FCTTkpiVM,8969
bitsandbytes/triton/int8_matmul_rowwise_dequantize.py,sha256=n38SCZ6Pt2GpHCJlUKjzKQEjJVuvUKLSTg1ORNW2LOE,8954
bitsandbytes/triton/matmul_perf_model.py,sha256=gaC6cu2m7PaGYl5CgCkEuCCZf9TL9J8MTAIdeKR6vxE,7431
bitsandbytes/triton/quantize_columnwise_and_transpose.py,sha256=jwbnRyjWRbD403CKxPOzF0JIzAmNcUandSq9AZaqqvw,2673
bitsandbytes/triton/quantize_global.py,sha256=L-nOgJoIv2sbiThisHBcHSzNiW4VkHlqXYdidE9TPws,4045
bitsandbytes/triton/quantize_rowwise.py,sha256=oaZ8xRSEFBc3bA-WrhHUwhQ7UIDnH6bEX0MzeeAbRJ4,2242
bitsandbytes/triton/triton_utils.py,sha256=95mMuH7SYNHz8EjcvWaKMN04FXbyOzN6_vD_YCRC17w,350
bitsandbytes/utils.py,sha256=k6QX_amGCJ4mCw74hjGLF8NZOV7f_QaysWUT-NA8e38,7032
