# # from transformers import AutoModelForVision2Seq, AutoProcessor
# # from huggingface_hub import login

# # # Step 1: Log in with your token (replace with your actual token)
# # login(token="*************************************")

# # # Step 2: Download and save the model locally
# # model = AutoModelForVision2Seq.from_pretrained(
# #     "google/medgemma-4b-it",
# #     token="hf_your_secret_token"
# # )
# # model.save_pretrained("./medgemma-4b-it")

# # # Step 3: Download and save the processor
# # processor = AutoProcessor.from_pretrained(
# #     "google/medgemma-4b-it",
# #     token="hf_your_secret_token"
# # )
# # processor.save_pretrained("./medgemma-4b-it")

# # from transformers import pipeline
# # from PIL import Image
# # import requests
# # import torch

# # pipe = pipeline(
# #     "image-text-to-text",
# #     model="google/medgemma-4b-it",
# #     torch_dtype=torch.bfloat16,
# #     device="cuda",
# # )

# from transformers import pipeline
# import torch

# # Ensure you are logged in: `huggingface-cli login`
# # and have accepted the model's license on Hugging Face Hub.

# model_id = "google/medgemma-4b-it"
# device = "cuda" if torch.cuda.is_available() else "cpu"
# dtype = torch.bfloat16 if torch.cuda.is_available() and torch.cuda.is_bf16_supported() else torch.float32

# print(f"Using device: {device}, dtype: {dtype}")

# try:
#     # For text generation
#     text_generator = pipeline(
#         "text-generation",
#         model=model_id,
#         torch_dtype=dtype,
#         device_map="auto", # Let accelerate handle device mapping for large models
#         # token=True # May be required if CLI login isn't picked up for some reason
#                      # Or pass your actual token string: token="hf_YOUR_TOKEN"
#     )

#     # MedGemma is an instruction-tuned model. You need to format your prompt accordingly.
#     # The exact format should be on the model card. It's often like:
#     # <start_of_turn>user
#     # Your question here<end_of_turn>
#     # <start_of_turn>model
#     # (The model will generate from here)

#     # Example prompt structure (VERIFY THIS FROM MEDGEMMA MODEL CARD)
#     chat = [
#         { "role": "user", "content": "What are the common symptoms of influenza, and how does it differ from a common cold?" },
#     ]
#     prompt = text_generator.tokenizer.apply_chat_template(chat, tokenize=False, add_generation_prompt=True)

#     print("\n--- Prompt ---")
#     print(prompt)

#     print("\n--- Generating Response ---")
#     # Note: For pipeline, it often handles tokenization internally for simple string inputs.
#     # However, for chat models, providing the structured prompt via apply_chat_template is best.
#     outputs = text_generator(
#         prompt,
#         max_new_tokens=250,
#         do_sample=True,
#         temperature=0.7,
#         top_k=50,
#         top_p=0.95
#     )

#     print("\n--- Model Response ---")
#     print(outputs[0]['generated_text']) # The output includes the prompt

# except Exception as e:
#     print(f"An error occurred: {e}")
#     print("Ensure you have accepted the model license on Hugging Face and are logged in.")
#     print("If you're getting OOM, consider quantization (see previous example) or a smaller model.")


import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, AutoConfig
import traceback # Import traceback module

# Disable dynamo compilation to avoid compilation issues
import torch._dynamo
torch._dynamo.config.suppress_errors = True

# Monkey patch to fix the ALL_PARALLEL_STYLES issue
def patch_parallel_styles():
    try:
        from transformers.modeling_utils import ALL_PARALLEL_STYLES
        if ALL_PARALLEL_STYLES is None:
            # Set it to include common parallel styles
            import transformers.modeling_utils
            transformers.modeling_utils.ALL_PARALLEL_STYLES = [
                "colwise", "rowwise", "sequence", "replicate", "shard"
            ]
            print("Patched ALL_PARALLEL_STYLES from None to include common styles")
        elif isinstance(ALL_PARALLEL_STYLES, list) and len(ALL_PARALLEL_STYLES) == 0:
            # If it's an empty list, add common styles
            import transformers.modeling_utils
            transformers.modeling_utils.ALL_PARALLEL_STYLES = [
                "colwise", "rowwise", "sequence", "replicate", "shard"
            ]
            print("Patched ALL_PARALLEL_STYLES from empty list to include common styles")
    except ImportError:
        print("Could not import ALL_PARALLEL_STYLES, skipping patch")
    except Exception as e:
        print(f"Error patching ALL_PARALLEL_STYLES: {e}")

# Apply the patch
patch_parallel_styles()

# --- Configuration ---
model_id = "google/medgemma-4b-it"
# Ensure you are logged in via `huggingface-cli login`
# and have accepted the model's license on Hugging Face Hub.
use_token = True # Assumes CLI login is done. Change to your token string if needed.

# --- Device Configuration ---
device = "cuda" if torch.cuda.is_available() else "cpu"
dtype = torch.bfloat16 if torch.cuda.is_available() and torch.cuda.is_bf16_supported() else torch.float16
if device == "cpu":
    dtype = torch.float32 # CPU usually uses float32 for better compatibility

print(f"Using model_id: {model_id}")
print(f"Using device: {device}")
print(f"Using dtype: {dtype}")
print(f"Using token: {use_token if isinstance(use_token, bool) else 'Explicit Token Provided'}")

# --- Load Config and Inspect/Modify ---
config = None # Initialize config to None
try:
    print(f"\nAttempting to load config for {model_id}...")
    config = AutoConfig.from_pretrained(model_id, token=use_token)
    print("Config loaded successfully.")

    # Fix parallelize_strategies issue
    if hasattr(config, "parallelize_strategies"):
        print(f"\nFound 'parallelize_strategies' in config: {config.parallelize_strategies}")
        if config.parallelize_strategies is None or \
           (isinstance(config.parallelize_strategies, list) and None in config.parallelize_strategies):
            print("WARNING: 'parallelize_strategies' is problematic (None or contains None).")
            print("Setting 'parallelize_strategies = []' on the loaded config object.")
            config.parallelize_strategies = []
        else:
            print("'parallelize_strategies' found and seems valid (not None, and no None elements if it's a list).")
    else:
        print("\n'parallelize_strategies' attribute not initially found in config.")
        print("Proactively setting 'parallelize_strategies = []' on the loaded config object.")
        config.parallelize_strategies = [] # Proactively set if not found

    # Fix text_config parallelize_strategies if it exists (for multi-modal models)
    if hasattr(config, 'text_config') and config.text_config is not None:
        if hasattr(config.text_config, "parallelize_strategies"):
            print(f"\nFound 'parallelize_strategies' in text_config: {config.text_config.parallelize_strategies}")
            if config.text_config.parallelize_strategies is None or \
               (isinstance(config.text_config.parallelize_strategies, list) and None in config.text_config.parallelize_strategies):
                print("WARNING: text_config 'parallelize_strategies' is problematic (None or contains None).")
                print("Setting text_config 'parallelize_strategies = []' on the loaded config object.")
                config.text_config.parallelize_strategies = []
        else:
            print("\n'parallelize_strategies' attribute not found in text_config.")
            print("Proactively setting text_config 'parallelize_strategies = []' on the loaded config object.")
            config.text_config.parallelize_strategies = []

except Exception as e:
    print(f"Error loading config: {e}")
    print("This might indicate an issue with model access, the model ID itself, or network issues.")
    traceback.print_exc()
    exit()

# --- Load Tokenizer ---
print(f"\nAttempting to load tokenizer for {model_id}...")
try:
    tokenizer = AutoTokenizer.from_pretrained(model_id, token=use_token)
    print("Tokenizer loaded successfully.")
except Exception as e:
    print(f"Error loading tokenizer: {e}")
    traceback.print_exc()
    exit()

# --- Attempt to Load Model ---
print(f"\nAttempting to load model {model_id}...")
model_kwargs = {"token": use_token}
if device == "cuda":
    model_kwargs["torch_dtype"] = dtype
    model_kwargs["device_map"] = "auto"
else: # CPU
    model_kwargs["torch_dtype"] = torch.float32


try:
    if config is None: # Should not happen if previous block didn't exit
        print("CRITICAL ERROR: Config object is None. Cannot proceed to load model.")
        exit()

    print(f"INFO: config.parallelize_strategies before model load: {getattr(config, 'parallelize_strategies', 'Attribute missing')}")

    # Try loading with the modified config first
    try:
        model = AutoModelForCausalLM.from_pretrained(
            model_id,       # Still use model_id for downloading weights
            config=config,  # Use our original or proactively modified config object
            **model_kwargs
        )
        print("Model loaded successfully with modified config.")
    except Exception as config_error:
        print(f"Failed to load with modified config: {config_error}")
        print("Attempting to load without passing config (let transformers handle it)...")

        # Try loading without passing the config - let transformers handle it internally
        model = AutoModelForCausalLM.from_pretrained(
            model_id,
            **model_kwargs
        )
        print("Model loaded successfully without custom config.")

    if hasattr(model, 'hf_device_map'):
        print("Model device map:", model.hf_device_map)
    else:
        print("Model is on device:", model.device)

except Exception as e:
    print(f"--------------------------------------------------------------------")
    print(f"CRITICAL ERROR: Error loading model {model_id}: {e}")
    print(f"--------------------------------------------------------------------")
    print("Full traceback for model loading error:")
    traceback.print_exc()
    print(f"--------------------------------------------------------------------")
    print("Potential issues to re-check:")
    print("1. Model ID is correct and exists on Hugging Face Hub.")
    print("2. You have accepted the license for this gated model on the Hub WHILE LOGGED IN.")
    print("3. You are correctly logged in via `huggingface-cli login` (or provided a valid token).")
    print("4. Your internet connection is stable.")
    print("5. Your `transformers`, `torch`, `accelerate` libraries are up-to-date and compatible.")
    print("   - `pip show transformers` (check version)")
    print("   - `pip install --upgrade transformers accelerate torch`")
    print("6. (Less likely for this error type) Insufficient VRAM/RAM, though OOM is usually more explicit.")
    exit()


# --- If Model Loaded Successfully, Try a Quick Generation ---
print("\n--- Preparing for Generation ---")
chat = [
    { "role": "user", "content": "What are the common symptoms of influenza, and how does it differ from a common cold?" },
]
try:
    prompt = tokenizer.apply_chat_template(chat, tokenize=False, add_generation_prompt=True)
except Exception as e:
    print(f"Could not apply chat template automatically: {e}")
    print("Manually creating prompt. YOU SHOULD VERIFY THE CORRECT FORMAT for MedGemma.")
    user_query = "What are the common symptoms of influenza, and how does it differ from a common cold?"
    prompt = f"<start_of_turn>user\n{user_query}<end_of_turn>\n<start_of_turn>model\n"

print("\n--- Prompt ---")
print(prompt)

input_device = device
if hasattr(model, 'hf_device_map') and model.hf_device_map:
    try:
        first_mapped_device = next(iter(model.hf_device_map.values()))
        if isinstance(first_mapped_device, int):
            input_device = f"cuda:{first_mapped_device}"
        elif isinstance(first_mapped_device, str):
            input_device = first_mapped_device
    except StopIteration:
        pass

print(f"Sending inputs to device: {input_device}")
inputs = tokenizer(prompt, return_tensors="pt").to(input_device)

print("\n--- Generating Response ---")
try:
    outputs = model.generate(
        **inputs,
        max_new_tokens=150,
        do_sample=True,
        temperature=0.7,
        top_k=50,
        top_p=0.95,
        eos_token_id=tokenizer.eos_token_id
    )
    response_text = tokenizer.decode(outputs[0][inputs.input_ids.shape[1]:], skip_special_tokens=True)

    print("\n--- Model Response ---")
    print(response_text)

except Exception as e:
    print(f"Error during generation: {e}")
    traceback.print_exc()